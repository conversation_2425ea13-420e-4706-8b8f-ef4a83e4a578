﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <OutputType>Library</OutputType>
    <AssemblyName>BHRCHACK</AssemblyName>
    <RootNamespace>BHRCHACK</RootNamespace>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <PlatformTarget>x86</PlatformTarget>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <!-- Note: External references are commented out.
       To use this plugin, you need to add the required DLL files to a 'lib' folder
       and uncomment the references below -->
  <!--
  <ItemGroup>
    <Reference Include="0Harmony">
      <HintPath>lib\0Harmony.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="Assembly-CSharp">
      <HintPath>lib\Assembly-CSharp.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="BepInEx.Core">
      <HintPath>lib\BepInEx.Core.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="BepInEx.IL2CPP">
      <HintPath>lib\BepInEx.IL2CPP.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="UnhollowerBaseLib">
      <HintPath>lib\UnhollowerBaseLib.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>lib\UnityEngine.CoreModule.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>lib\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>lib\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>lib\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>false</Private>
    </Reference>
  </ItemGroup>
  -->

  <ItemGroup>
    <Compile Include="PluginInfo.cs" />
    <!-- Uncomment the lines below when you have the required references -->
    <!--
    <Compile Include="cheat\aim\aimbot.cs" />
    <Compile Include="cheat\visual\esp.cs" />
    <Compile Include="ExecuteCheat.cs" />
    <Compile Include="Plugin.cs" />
    <Compile Include="Render.cs" />
    -->
  </ItemGroup>
</Project>