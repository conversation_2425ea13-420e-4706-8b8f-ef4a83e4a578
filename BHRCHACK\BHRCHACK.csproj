﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProjectGuid>{8E75EDAB-9521-40B1-8A2E-68D93271E6BE}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>BHRCHACK</RootNamespace>
    <AssemblyName>BHRCHACK</AssemblyName>
    <TargetFrameworkVersion>v2.1</TargetFrameworkVersion>
    <TargetFrameworkIdentifier>.NETStandard</TargetFrameworkIdentifier>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="0Harmony" />
    <Reference Include="Assembly-CSharp" />
    <Reference Include="BepInEx.Core" />
    <Reference Include="BepInEx.IL2CPP" />
    <Reference Include="netstandard" />
    <Reference Include="UnhollowerBaseLib" />
    <Reference Include="UnityEngine.CoreModule" />
    <Reference Include="UnityEngine.IMGUIModule" />
    <Reference Include="UnityEngine.PhysicsModule" />
    <Reference Include="UnityEngine.TextRenderingModule" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="cheat\aim\aimbot.cs" />
    <Compile Include="cheat\visual\esp.cs" />
    <Compile Include="ExecuteCheat.cs" />
    <Compile Include="Plugin.cs" />
    <Compile Include="PluginInfo.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Render.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>