﻿using System;
using UnityEngine;

namespace BHRCHACK
{
	// Token: 0x02000004 RID: 4
	public class Render : MonoBehaviour
	{
		// Token: 0x17000001 RID: 1
		// (get) Token: 0x0600000C RID: 12 RVA: 0x000027D9 File Offset: 0x000009D9
		// (set) Token: 0x0600000D RID: 13 RVA: 0x000027E0 File Offset: 0x000009E0
		public static Color Color
		{
			get
			{
				return GUI.color;
			}
			set
			{
				GUI.color = value;
			}
		}

		// Token: 0x0600000E RID: 14 RVA: 0x000027E8 File Offset: 0x000009E8
		public static void DrawLine(Vector2 pointA, Vector2 pointB, Color color, float width)
		{
			Matrix4x4 matrix = GUI.matrix;
			if (!Render.lineTex)
			{
				Render.lineTex = new Texture2D(1, 1);
			}
			Color color2 = GUI.color;
			GUI.color = color;
			float num = Vector3.Angle(pointB - pointA, Vector2.right);
			if (pointA.y > pointB.y)
			{
				num = -num;
			}
			GUIUtility.ScaleAroundPivot(new Vector2((pointB - pointA).magnitude, width), new Vector2(pointA.x, pointA.y + 0.5f));
			GUIUtility.RotateAroundPivot(num, pointA);
			GUI.DrawTexture(new Rect(pointA.x, pointA.y, 1f, 1f), Render.lineTex);
			GUI.matrix = matrix;
			GUI.color = color2;
		}

		// Token: 0x0600000F RID: 15 RVA: 0x000028B4 File Offset: 0x00000AB4
		public static void DrawCircle(Vector2 center, float radius, Color color, float numSegments = 40f)
		{
			Quaternion quaternion = Quaternion.AngleAxis(360f / numSegments, Vector3.forward);
			Vector2 vector;
			vector..ctor(radius, 0f);
			int num = 0;
			while ((float)num < numSegments)
			{
				Vector2 vector2 = quaternion * vector;
				Render.DrawLine(center + vector, center + vector2, color, 2f);
				vector = vector2;
				num++;
			}
		}

		// Token: 0x06000010 RID: 16 RVA: 0x0000291C File Offset: 0x00000B1C
		public static void DrawBox(float x, float y, float w, float h, Color color, float thickness)
		{
			Render.DrawLine(new Vector2(x, y), new Vector2(x + w, y), color, thickness);
			Render.DrawLine(new Vector2(x, y), new Vector2(x, y + h), color, thickness);
			Render.DrawLine(new Vector2(x + w, y), new Vector2(x + w, y + h), color, thickness);
			Render.DrawLine(new Vector2(x, y + h), new Vector2(x + w, y + h), color, thickness);
		}

		// Token: 0x04000017 RID: 23
		public static Texture2D lineTex;
	}
}
