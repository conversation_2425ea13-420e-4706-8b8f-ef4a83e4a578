﻿using System;
using BepInEx.Logging;
using Player;
using UnityEngine;

namespace BHRCHACK.cheat.visual
{
	// Token: 0x02000006 RID: 6
	public class esp
	{
		// Token: 0x06000012 RID: 18 RVA: 0x000029A0 File Offset: 0x00000BA0
		public static void run_esp()
		{
			try
			{
				esp.DrawESP();
			}
			catch
			{
				Plugin.Logs.LogError("Failed to run method : (BHRCHACK.cheat.visual.DrawESP)..");
			}
		}

		// Token: 0x06000013 RID: 19 RVA: 0x000029D8 File Offset: 0x00000BD8
		public static void DrawESP()
		{
			PlayerData pl = Controll.pl;
			Camera csCam = Controll.csCam;
			Vector3 currPos = Controll.currPos;
			if (pl == null)
			{
				ManualLogSource logs = Plugin.Logs;
				string text = "LocalPlayer (";
				PlayerData playerData = pl;
				logs.LogError(text + ((playerData != null) ? playerData.ToString() : null) + ") is null! failed to run cheat.aim.esp.DrawESP().. Please connect to a server!");
			}
			foreach (PlayerData playerData2 in Plugin.players)
			{
				if (playerData2 != null && playerData2.health > 10 && pl.name != playerData2.name)
				{
					Vector3 position = playerData2.tr.position;
					Vector3 position2 = playerData2.rbHead.position;
					Vector3 vector = new Vector3(position.x, position.y - 2.1f, position.z);
					Vector3 vector2 = new Vector3(position2.x, position2.y + 0.7f, position2.z);
					Vector3 vector3 = csCam.WorldToScreenPoint(position);
					Vector3 vector4 = csCam.WorldToScreenPoint(vector2);
					Vector3 vector5 = csCam.WorldToScreenPoint(vector);
					if (vector3.z > 0f)
					{
						float num = vector4.y - vector5.y;
						float num2 = 2f;
						float num3 = num / num2;
						Rect rect = new Rect(vector4.x - 20f, (float)Screen.height - vector4.y - 15f, 200f, 50f);
						Rect rect2 = new Rect(vector5.x - 15f, (float)Screen.height - vector5.y + 3f, 200f, 50f);
						Rect rect3 = new Rect(vector4.x - 20f, (float)Screen.height - vector4.y - 25f, 200f, 50f);
						bool flag = playerData2.team == pl.team;
						GUIStyle guistyle = new GUIStyle();
						guistyle.normal.textColor = Color.white;
						guistyle.fontSize = 10;
						guistyle.fontStyle = 1;
						if (Plugin.esp_name)
						{
							GUI.Label(rect, playerData2.name, guistyle);
						}
						if (Plugin.esp_weapon)
						{
							GUI.Label(rect3, "[weapon]" + playerData2.currweapon.weaponname, guistyle);
						}
						if (Plugin.esp_health)
						{
							GUI.Label(rect2, "[HP]:" + playerData2.health.ToString(), guistyle);
						}
						if (Plugin.esp_box)
						{
							if (Plugin.teamchecking)
							{
								if (!flag)
								{
									Render.DrawBox(vector5.x - num3 / 2f, (float)Screen.height - vector5.y - num, num3, num, Color.yellow, 1f);
								}
								else
								{
									Render.DrawBox(vector5.x - num3 / 2f, (float)Screen.height - vector5.y - num, num3, num, Color.blue, 1f);
								}
							}
							else
							{
								Render.DrawBox(vector5.x - num3 / 2f, (float)Screen.height - vector5.y - num, num3, num, Color.yellow, 1f);
							}
						}
						if (Plugin.esp_line)
						{
							if (Plugin.teamchecking)
							{
								if (!flag)
								{
									Render.DrawLine(new Vector2((float)Screen.height, (float)Screen.height), new Vector2(vector5.x, (float)Screen.height - vector5.y), Color.red, 1f);
								}
								else
								{
									Render.DrawLine(new Vector2((float)Screen.height, (float)Screen.height), new Vector2(vector5.x, (float)Screen.height - vector5.y), Color.white, 1f);
								}
							}
							else
							{
								Render.DrawLine(new Vector2((float)Screen.height, (float)Screen.height), new Vector2(vector5.x, (float)Screen.height - vector5.y), Color.red, 1f);
							}
						}
					}
				}
			}
		}
	}
}
