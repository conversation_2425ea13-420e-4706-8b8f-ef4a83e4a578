﻿using System;
using System.Collections;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using BepInEx.Logging;
using Player;
using UnityEngine;

namespace BHRCHACK.cheat.aim
{
	// Token: 0x02000007 RID: 7
	public class aimbot
	{
		// Token: 0x06000015 RID: 21
		[DllImport("user32.dll")]
		public static extern void mouse_event(int dwFlags, int dx, int dy, int dwData, int dwExtraInfo);

		// Token: 0x06000016 RID: 22 RVA: 0x00002E04 File Offset: 0x00001004
		public static IEnumerator WaitAndPrint(float waitTime)
		{
			yield return new WaitForSecondsRealtime(waitTime);
			Plugin.Logs.LogDebug("Coroutine ended: " + Time.time.ToString() + " seconds");
			yield break;
		}

		// Token: 0x06000017 RID: 23 RVA: 0x00002E13 File Offset: 0x00001013
		private static void LeftClick()
		{
			aimbot.WaitAndPrint(0.025f);
			aimbot.mouse_event(2, 0, 0, 0, 0);
			aimbot.WaitAndPrint(0.025f);
			aimbot.mouse_event(4, 0, 0, 0, 0);
			aimbot.WaitAndPrint(0.025f);
		}

		// Token: 0x06000018 RID: 24 RVA: 0x00002E4C File Offset: 0x0000104C
		public static void run_aimbot()
		{
			try
			{
				aimbot.GetBestTarget();
			}
			catch
			{
				Plugin.Logs.LogError("Failed to run method : (BHRCHACK.cheat.aim.GetBestTarget)..");
			}
			try
			{
				aimbot.AimPlayer();
			}
			catch
			{
				Plugin.Logs.LogError("Failed to run method : (BHRCHACK.cheat.aim.AimPlayer)..");
			}
		}

		// Token: 0x06000019 RID: 25 RVA: 0x00002EA8 File Offset: 0x000010A8
		public static void GetBestTarget()
		{
			aimbot.<>c__DisplayClass5_0 CS$<>8__locals1;
			CS$<>8__locals1.localPlayer = Controll.pl;
			if (CS$<>8__locals1.localPlayer == null)
			{
				ManualLogSource logs = Plugin.Logs;
				string text = "LocalPlayer (";
				PlayerData localPlayer = CS$<>8__locals1.localPlayer;
				logs.LogError(text + ((localPlayer != null) ? localPlayer.ToString() : null) + ") is null! failed to run cheat.aim.aimbot.GetBestTarget().. Please connect to a server!");
			}
			CS$<>8__locals1.localPlayerCamera = Controll.csCam;
			Vector3 currPos = Controll.currPos;
			float num = float.MaxValue;
			foreach (PlayerData player in Plugin.players)
			{
				aimbot.<>c__DisplayClass5_1 CS$<>8__locals2;
				CS$<>8__locals2.player = player;
				if (CS$<>8__locals2.player != null)
				{
					if (Plugin.trigger_rage && aimbot.<GetBestTarget>g__IsInCross|5_0(ref CS$<>8__locals1, ref CS$<>8__locals2))
					{
						aimbot.LeftClick();
					}
					Vector3 position = CS$<>8__locals2.player.rbHead.position;
					Vector3 vector;
					vector..ctor(position.x, position.y + 0.45f, position.z);
					Vector3 vector2 = CS$<>8__locals1.localPlayerCamera.WorldToScreenPoint(vector);
					if (vector2.z > 0f && CS$<>8__locals2.player.health > 10 && CS$<>8__locals1.localPlayer.name != CS$<>8__locals2.player.name && !CS$<>8__locals2.player.spawnprotect)
					{
						Vector2 vector3 = new Vector2(vector2.x, (float)Screen.height - vector2.y);
						Vector2 vector4;
						vector4..ctor((float)(Screen.width / 2), (float)(Screen.height / 2));
						float num2 = Math.Abs(Vector2.Distance(vector3, vector4));
						bool flag = num2 < num;
						bool flag2 = CS$<>8__locals2.player.team == CS$<>8__locals1.localPlayer.team;
						if (num2 < Plugin.aim_fov && flag)
						{
							if (Plugin.teamchecking)
							{
								if (!flag2)
								{
									num = num2;
									aimbot.vectorToAimAt = new Vector2(vector2.x, (float)Screen.height - vector2.y);
								}
							}
							else
							{
								num = num2;
								aimbot.vectorToAimAt = new Vector2(vector2.x, (float)Screen.height - vector2.y);
							}
						}
					}
				}
			}
		}

		// Token: 0x0600001A RID: 26 RVA: 0x000030D4 File Offset: 0x000012D4
		public static void AimPlayer()
		{
			bool flag = aimbot.vectorToAimAt != Vector2.zero;
			if (Plugin.aimbot && !Plugin.show && flag)
			{
				double num = (double)(aimbot.vectorToAimAt.x - (float)Screen.width / 2f);
				double num2 = (double)(aimbot.vectorToAimAt.y - (float)Screen.height / 2f);
				num /= (double)Plugin.aim_smooth;
				num2 /= (double)Plugin.aim_smooth;
				if (Input.GetKey(324) || Input.GetKey(323))
				{
					aimbot.mouse_event(1, (int)num, (int)num2, 0, 0);
				}
			}
		}

		// Token: 0x0600001D RID: 29 RVA: 0x00003184 File Offset: 0x00001384
		[CompilerGenerated]
		internal static bool <GetBestTarget>g__IsInCross|5_0(ref aimbot.<>c__DisplayClass5_0 A_0, ref aimbot.<>c__DisplayClass5_1 A_1)
		{
			if (A_1.player == A_0.localPlayer)
			{
				return false;
			}
			int team = A_1.player.team;
			int team2 = A_0.localPlayer.team;
			RaycastHit raycastHit;
			if (Physics.Raycast(A_0.localPlayerCamera.ScreenPointToRay(Input.mousePosition), ref raycastHit, float.PositiveInfinity))
			{
				if (raycastHit.transform.name == "Map" || raycastHit.transform.name == "MapBackPlatform" || raycastHit.transform.name.Contains("p_weapon"))
				{
					return false;
				}
				string name = raycastHit.transform.name;
				if (name == "head" || name == "body" || name == "RightArmUp" || name == "RightArmDown" || name == "LeftArmUp" || name == "LeftArmDown" || name == "RightLegUp" || name == "RightLegDown" || name == "LeftLegUp" || name == "LeftLegDown" || name == "RightLegBoot" || name == "LeftLegBoot")
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x0400001B RID: 27
		public static Vector2 vectorToAimAt = Vector2.zero;
	}
}
