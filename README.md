# BHRCHACK - Game Cheat Plugin

## الوصف
هذا مشروع plugin للعبة يحتوي على ميزات cheat مختلفة مثل aimbot و ESP.

## متطلبات التجميع

### المكتبات المطلوبة
لتجميع المشروع بالكامل، تحتاج إلى إضافة المكتبات التالية في مجلد `lib`:

- `0Harmony.dll`
- `Assembly-CSharp.dll` 
- `BepInEx.Core.dll`
- `BepInEx.IL2CPP.dll`
- `UnhollowerBaseLib.dll`
- `UnityEngine.CoreModule.dll`
- `UnityEngine.IMGUIModule.dll`
- `UnityEngine.PhysicsModule.dll`
- `UnityEngine.TextRenderingModule.dll`

### خطوات التجميع

1. **التجميع الأساسي (بدون المراجع الخارجية):**
   ```bash
   dotnet build BHRCHACK/BHRCHACK.csproj --configuration Release
   ```
   هذا سينتج ملف DLL أساسي يحتوي على `PluginInfo.cs` فقط.

2. **التجميع الكامل (مع المراجع الخارجية):**
   - أنشئ مجلد `BHRCHACK/lib`
   - ضع جميع ملفات DLL المطلوبة في هذا المجلد
   - قم بإلغاء التعليق عن المراجع في ملف `BHRCHACK.csproj`
   - قم بإلغاء التعليق عن ملفات الكود في قسم `<Compile Include>`
   - قم بتشغيل الأمر:
   ```bash
   dotnet build BHRCHACK/BHRCHACK.csproj --configuration Release
   ```

## بنية المشروع

```
BHRCHACK/
├── cheat/
│   ├── aim/
│   │   └── aimbot.cs          # وظائف الـ aimbot
│   └── visual/
│       └── esp.cs             # وظائف الـ ESP
├── Properties/
│   └── AssemblyInfo.cs        # معلومات التجميع
├── ExecuteCheat.cs            # تنفيذ الـ cheats والواجهة
├── Plugin.cs                  # الـ plugin الرئيسي
├── PluginInfo.cs              # معلومات الـ plugin
├── Render.cs                  # وظائف الرسم
└── BHRCHACK.csproj           # ملف المشروع
```

## الميزات

- **Aimbot**: تصويب تلقائي على الأعداء
- **ESP**: عرض معلومات اللاعبين (الاسم، الصحة، السلاح)
- **Triggerbot**: إطلاق نار تلقائي عند التصويب على العدو
- **Team Check**: فحص الفريق لتجنب إصابة أعضاء الفريق

## ملاحظات الأمان

⚠️ **تحذير**: هذا المشروع مخصص للأغراض التعليمية فقط. استخدام cheats في الألعاب قد يؤدي إلى:
- حظر الحساب
- انتهاك شروط الخدمة
- مشاكل قانونية

## الاستخدام

بعد التجميع الناجح، ستجد ملف `BHRCHACK.dll` في:
```
BHRCHACK/bin/Release/netstandard2.1/BHRCHACK.dll
```

## المساهمة

هذا المشروع مفتوح المصدر. يمكنك المساهمة بـ:
- إصلاح الأخطاء
- إضافة ميزات جديدة
- تحسين الكود
- تحديث التوثيق

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
