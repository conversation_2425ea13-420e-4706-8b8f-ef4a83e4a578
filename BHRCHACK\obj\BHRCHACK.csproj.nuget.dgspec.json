{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\ty\\BHRCHACK\\BHRCHACK.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\ty\\BHRCHACK\\BHRCHACK.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\ty\\BHRCHACK\\BHRCHACK.csproj", "projectName": "BHRCHACK", "projectPath": "C:\\Users\\<USER>\\Downloads\\ty\\BHRCHACK\\BHRCHACK.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\ty\\BHRCHACK\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.410\\RuntimeIdentifierGraph.json"}}}}}