﻿using System;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;
using System.Security;
using System.Security.Permissions;

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyCompany("BHRCHACK")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyDescription("My first plugin")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyInformationalVersion("1.0.0")]
[assembly: AssemblyProduct("BHRCHACK")]
[assembly: AssemblyTitle("BHRCHACK")]
[assembly: SecurityPermission(8, SkipVerification = true)]
