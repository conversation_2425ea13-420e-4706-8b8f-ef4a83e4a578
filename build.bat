@echo off
echo ========================================
echo BHRCHACK DLL Builder
echo ========================================
echo.

REM Check if dotnet is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET SDK is not installed or not in PATH
    echo Please install .NET SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET SDK found: 
dotnet --version
echo.

REM Clean previous builds
echo Cleaning previous builds...
if exist "BHRCHACK\bin" rmdir /s /q "BHRCHACK\bin"
if exist "BHRCHACK\obj" rmdir /s /q "BHRCHACK\obj"
echo.

REM Build the project
echo Building BHRCHACK.dll...
dotnet build BHRCHACK\BHRCHACK.csproj --configuration Release --verbosity minimal

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo Build completed successfully!
    echo ========================================
    echo.
    echo Output file: BHRCHACK\bin\Release\netstandard2.1\BHRCHACK.dll
    echo.
    
    REM Show file size
    for %%I in ("BHRCHACK\bin\Release\netstandard2.1\BHRCHACK.dll") do (
        echo File size: %%~zI bytes
    )
    echo.
    
    REM Check if lib folder exists
    if not exist "BHRCHACK\lib" (
        echo Note: To build with full functionality, create a 'lib' folder
        echo in BHRCHACK directory and add the required DLL files.
        echo See README.md for more details.
        echo.
    )
    
    echo Build completed at: %date% %time%
) else (
    echo.
    echo ========================================
    echo Build failed!
    echo ========================================
    echo Please check the error messages above.
)

echo.
pause
