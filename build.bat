@echo off
echo ========================================
echo    BHRCHACK Protected Build Script
echo ========================================
echo.

REM Check if dotnet is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found!
    echo Please install .NET SDK from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo [1/4] Checking project structure...
if not exist "BHRCHACK\BHRCHACK.csproj" (
    echo ERROR: Project file not found!
    echo Make sure you're running this from the correct directory.
    pause
    exit /b 1
)

echo [2/4] Creating libs directory...
if not exist "BHRCHACK\libs" mkdir "BHRCHACK\libs"

echo [3/4] Checking for required references...
echo.
echo IMPORTANT: You need to copy the following DLL files to BHRCHACK\libs\:
echo.
echo From your game directory:
echo   - Assembly-CSharp.dll
echo.
echo From BepInEx directory:
echo   - BepInEx.Core.dll
echo   - BepInEx.IL2CPP.dll
echo   - 0Harmony.dll
echo   - UnhollowerBaseLib.dll
echo.
echo From Unity (usually in game's managed folder):
echo   - UnityEngine.CoreModule.dll
echo   - UnityEngine.IMGUIModule.dll
echo   - UnityEngine.PhysicsModule.dll
echo   - UnityEngine.TextRenderingModule.dll
echo.

set /p continue="Have you copied all required DLLs? (y/n): "
if /i "%continue%" neq "y" (
    echo.
    echo Please copy the required DLLs first, then run this script again.
    pause
    exit /b 1
)

echo [4/4] Building project...
cd BHRCHACK
dotnet build --configuration Release --verbosity minimal

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo         BUILD SUCCESSFUL! 
    echo ========================================
    echo.
    echo Your protected DLL is ready at:
    echo BHRCHACK\bin\Release\netstandard2.1\BHRCHACK.dll
    echo.
    echo Features added:
    echo  ✓ Anti-detection protection
    echo  ✓ String encryption
    echo  ✓ API call obfuscation
    echo  ✓ Memory data scrambling
    echo  ✓ Anti-cheat detection
    echo  ✓ VM detection
    echo  ✓ Debugger detection
    echo.
) else (
    echo.
    echo ========================================
    echo           BUILD FAILED!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo Make sure all required DLL references are in the libs folder.
)

cd ..
echo.
pause
