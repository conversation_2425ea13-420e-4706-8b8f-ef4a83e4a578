﻿using System;
using System.Reflection;
using BepInEx;
using BepInEx.IL2CPP;
using BepInEx.Logging;
using HarmonyLib;
using Player;
using UnhollowerBaseLib;
using UnhollowerRuntimeLib;
using UnityEngine;

namespace BHRCHACK
{
	// Token: 0x02000003 RID: 3
	[BepInPlugin("BRHC", "BRHC", "1.0.1")]
	public class Plugin : BasePlugin
	{
		// Token: 0x06000007 RID: 7 RVA: 0x00002593 File Offset: 0x00000793
		public Plugin()
		{
			Plugin.Logs = base.Log;
		}

		// Token: 0x06000008 RID: 8 RVA: 0x000025BC File Offset: 0x000007BC
		public override void Load()
		{
			try
			{
				base.Log.LogInfo("Plugin BRHC is loaded!");
				ClassInjector.RegisterTypeInIl2Cpp<ExecuteCheat>();
				base.Log.LogMessage("Registered hack class type into il2cpp!");
				this.CreateHackObject(this.hackGameObjectName);
				base.Log.LogMessage("Created new hack GameObject!");
				this.HarmonyPatching(this.harmonyID);
				base.Log.LogMessage("Patched game functions with hack functions!");
			}
			catch
			{
				base.Log.LogError("Failed to load hack...");
			}
		}

		// Token: 0x06000009 RID: 9 RVA: 0x0000264C File Offset: 0x0000084C
		public void CreateHackObject(string gameObjectName)
		{
			try
			{
				GameObject gameObject = new GameObject(gameObjectName);
				gameObject.AddComponent<ExecuteCheat>();
				Object.DontDestroyOnLoad(gameObject);
			}
			catch
			{
				base.Log.LogError("Failed to create hack GameObject...");
			}
		}

		// Token: 0x0600000A RID: 10 RVA: 0x00002690 File Offset: 0x00000890
		public void HarmonyPatching(string id)
		{
			Harmony harmony = new Harmony(id);
			try
			{
				MethodInfo methodInfo = AccessTools.Method(typeof(Client), "Update", null, null);
				MethodInfo methodInfo2 = AccessTools.Method(typeof(ExecuteCheat), "Update", null, null);
				harmony.Patch(methodInfo, null, new HarmonyMethod(methodInfo2), null, null, null);
			}
			catch
			{
				base.Log.LogError("Failed to patch Update method...");
			}
			try
			{
				MethodInfo methodInfo3 = AccessTools.Method(typeof(Main), "OnGUI", null, null);
				MethodInfo methodInfo4 = AccessTools.Method(typeof(ExecuteCheat), "OnGUI", null, null);
				harmony.Patch(methodInfo3, null, new HarmonyMethod(methodInfo4), null, null, null);
			}
			catch
			{
				base.Log.LogError("Failed to path OnGUI method...");
			}
		}

		// Token: 0x04000002 RID: 2
		public static Il2CppReferenceArray<PlayerData> players;

		// Token: 0x04000003 RID: 3
		public static bool show = true;

		// Token: 0x04000004 RID: 4
		public static bool espMenu = false;

		// Token: 0x04000005 RID: 5
		public static bool aimMenu = false;

		// Token: 0x04000006 RID: 6
		public static bool aimbot = false;

		// Token: 0x04000007 RID: 7
		public static float aim_smooth = 1.35f;

		// Token: 0x04000008 RID: 8
		public static float aim_fov = 220f;

		// Token: 0x04000009 RID: 9
		public static bool trigger_rage = false;

		// Token: 0x0400000A RID: 10
		public static bool silentaim = false;

		// Token: 0x0400000B RID: 11
		public static bool esp_box = false;

		// Token: 0x0400000C RID: 12
		public static bool esp_weapon = false;

		// Token: 0x0400000D RID: 13
		public static bool esp_line = false;

		// Token: 0x0400000E RID: 14
		public static bool esp_name = false;

		// Token: 0x0400000F RID: 15
		public static bool esp_health = false;

		// Token: 0x04000010 RID: 16
		public static bool teamchecking = true;

		// Token: 0x04000011 RID: 17
		public const string PLUGIN_GUID = "BRHC";

		// Token: 0x04000012 RID: 18
		public const string PLUGIN_NAME = "BRHC";

		// Token: 0x04000013 RID: 19
		public const string PLUGIN_VERSION = "1.0.1";

		// Token: 0x04000014 RID: 20
		public static ManualLogSource Logs;

		// Token: 0x04000015 RID: 21
		public string hackGameObjectName = "HackGameObject";

		// Token: 0x04000016 RID: 22
		public string harmonyID = "HarmonyPatching";
	}
}
