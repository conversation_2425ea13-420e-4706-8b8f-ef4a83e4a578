﻿using System;
using System.Collections;
using System.Runtime.CompilerServices;
using BHRCHACK.cheat.aim;
using BHRCHACK.cheat.visual;
using HarmonyLib;
using UnityEngine;

namespace BHRCHACK
{
	// Token: 0x02000002 RID: 2
	public class ExecuteCheat : MonoBehaviour
	{
		// Token: 0x06000001 RID: 1 RVA: 0x00002050 File Offset: 0x00000250
		public static IEnumerator update_player_array()
		{
			Plugin.players = PLH.player;
			yield return new WaitForSeconds(3f);
			yield break;
		}

		// Token: 0x06000002 RID: 2 RVA: 0x00002058 File Offset: 0x00000258
		[HarmonyPostfix]
		public static void Update()
		{
			Plugin.players = PLH.player;
			try
			{
				aimbot.run_aimbot();
			}
			catch
			{
				Plugin.Logs.LogError("Failed to run method : (BHRCHACK.cheat.aim.run_aimbot)..");
			}
		}

		// Token: 0x06000003 RID: 3 RVA: 0x00002098 File Offset: 0x00000298
		[HarmonyPostfix]
		public static void OnGUI()
		{
			GUIStyle guistyle = new GUIStyle();
			Color textColor = Color.HSVToRGB(Mathf.PingPong(Time.time * 0.3f, 1f), 1f, 1f);
			guistyle.alignment = 0;
			guistyle.normal.textColor = textColor;
			guistyle.fontSize = 20;
			GUI.Label(new Rect(0f, 0f, 120f, 30f), "BRHC CHEAT V1.1", guistyle);
			if (Input.GetKeyDown(280))
			{
				Plugin.show = true;
			}
			if (Input.GetKeyDown(281))
			{
				Plugin.show = false;
			}
			if (Plugin.show)
			{
				ExecuteCheat.gui_cheat = GUI.Window(0, ExecuteCheat.gui_cheat, GUIMainWindow, "BRHC - GUI");
			}
			try
			{
				esp.run_esp();
			}
			catch
			{
				Plugin.Logs.LogError("Failed to run method : (BHRCHACK.cheat.visual.esp.run_esp)..");
			}
		}

		// Token: 0x06000006 RID: 6 RVA: 0x000021B4 File Offset: 0x000003B4
		internal static void GUIMainWindow(int windowID)
		{
			GUI.backgroundColor = Color.black;
			float num = 80f;
			if (Plugin.show)
			{
				if (GUI.Button(new Rect(0f, 20f, 120f, 25f), "AIM"))
				{
					Plugin.aimMenu = !Plugin.aimMenu;
					Plugin.espMenu = false;
				}
				if (GUI.Button(new Rect(120f, 20f, 120f, 25f), "ESP"))
				{
					Plugin.espMenu = !Plugin.espMenu;
					Plugin.aimMenu = false;
				}
				if (Plugin.aimMenu)
				{
					Plugin.aimbot = GUI.Toggle(new Rect(0f, 45f, 160f, 30f), Plugin.aimbot, "Aimbot");
					Plugin.silentaim = GUI.Toggle(new Rect(0f, num, 160f, 30f), Plugin.silentaim, "SilentAim");
					Plugin.trigger_rage = GUI.Toggle(new Rect(0f, num + 40f, 160f, 30f), Plugin.trigger_rage, "TriggerBot");
					GUI.Label(new Rect(5f, num + 40f + 20f, 160f, 30f), "AimFOV");
					Plugin.aim_fov = GUI.HorizontalSlider(new Rect(0f, num + 40f + 40f, 200f, 30f), Plugin.aim_fov, 30f, 1000f);
					GUI.Label(new Rect(5f, num + 40f + 40f + 20f, 160f, 30f), "AimSmooth");
					Plugin.aim_smooth = GUI.HorizontalSlider(new Rect(0f, num + 40f + 40f + 40f, 200f, 30f), Plugin.aim_smooth, 1.3f, 20f);
					GUI.Label(new Rect(5f, num + 40f + 40f + 40f + 40f + 5f, 190f, 30f), "AimSmooth: " + Plugin.aim_smooth.ToString());
					GUI.Label(new Rect(5f, num + 40f + 40f + 40f + 40f + 40f + 5f, 190f, 30f), "AimFOV: " + Plugin.aim_fov.ToString());
				}
				if (Plugin.espMenu)
				{
					Plugin.esp_box = GUI.Toggle(new Rect(0f, 45f, 160f, 30f), Plugin.esp_box, "Box 2D");
					Plugin.esp_line = GUI.Toggle(new Rect(0f, num, 160f, 30f), Plugin.esp_line, "Tracers");
					Plugin.esp_name = GUI.Toggle(new Rect(0f, num + 40f, 160f, 30f), Plugin.esp_name, "Name");
					Plugin.esp_health = GUI.Toggle(new Rect(0f, num + 40f + 40f, 160f, 30f), Plugin.esp_health, "Health");
					Plugin.esp_weapon = GUI.Toggle(new Rect(0f, num + 40f + 40f + 40f, 160f, 30f), Plugin.esp_weapon, "Weapon");
					Plugin.teamchecking = GUI.Toggle(new Rect(0f, num + 40f + 40f + 40f + 40f, 180f, 39f), Plugin.teamchecking, "TeamCheck (Aimbot & ESP)");
				}
			}
			GUI.DragWindow();
		}

		// Token: 0x04000001 RID: 1
		public static Rect gui_cheat = new Rect(320f, 120f, 230f, 320f);
	}
}
